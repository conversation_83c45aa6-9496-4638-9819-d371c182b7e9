import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { WebhookVerificationService } from '../services/webhook-verification.service';

@Injectable()
export class WebhookVerificationGuard implements CanActivate {
  private readonly logger = new Logger(WebhookVerificationGuard.name);

  constructor(
    private readonly webhookVerificationService: WebhookVerificationService,
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const headers = request.headers;
    const body = request.rawBody || request.body;

    // Log webhook attempt for debugging
    this.logger.debug('Webhook verification attempt', {
      hasShopifySignature: !!headers['x-shopify-hmac-sha256'],
      hasCustomSecret: !!(
        headers['x-webhook-secret'] || headers['authorization']
      ),
      contentType: headers['content-type'],
      userAgent: headers['user-agent'],
    });

    // Verify the webhook
    const isValid = this.webhookVerificationService.verifyWebhookFromHeaders(
      headers,
      body,
    );

    if (!isValid) {
      this.logger.warn('Webhook verification failed', {
        ip: request.ip,
        userAgent: headers['user-agent'],
        contentType: headers['content-type'],
      });
      throw new UnauthorizedException('Invalid webhook signature or secret');
    }

    this.logger.log('Webhook verification successful');
    return true;
  }
}
