import { Injectable, Logger } from '@nestjs/common';
import * as crypto from 'crypto';

@Injectable()
export class WebhookVerificationService {
  private readonly logger = new Logger(WebhookVerificationService.name);
  private readonly webhookSecret: string;

  constructor() {
    this.webhookSecret = process.env.SHOPIFY_WEBHOOK_SECRET;
    if (!this.webhookSecret) {
      this.logger.error(
        'SHOPIFY_WEBHOOK_SECRET environment variable is not set',
      );
      throw new Error('Webhook secret not configured');
    }
  }

  /**
   * Verify Shopify webhook signature using HMAC-SHA256
   * @param body - Raw request body as string or Buffer
   * @param signature - Signature from X-Shopify-Hmac-Sha256 header
   * @returns boolean indicating if signature is valid
   */
  verifyShopifyWebhook(body: string | Buffer, signature: string): boolean {
    try {
      if (!signature) {
        this.logger.warn('No signature provided for webhook verification');
        return false;
      }

      // Ensure body is a Buffer for consistent hashing
      const bodyBuffer = Buffer.isBuffer(body)
        ? body
        : Buffer.from(body, 'utf8');

      // Create HMAC hash using the webhook secret
      const hmac = crypto.createHmac('sha256', this.webhookSecret);
      hmac.update(bodyBuffer);
      const calculatedSignature = hmac.digest('base64');

      // Compare signatures using timing-safe comparison
      const isValid = crypto.timingSafeEqual(
        Buffer.from(signature, 'base64'),
        Buffer.from(calculatedSignature, 'base64'),
      );

      if (!isValid) {
        this.logger.warn('Webhook signature verification failed', {
          providedSignature: signature.substring(0, 10) + '...',
          calculatedSignature: calculatedSignature.substring(0, 10) + '...',
        });
      }

      return isValid;
    } catch (error) {
      this.logger.error('Error during webhook signature verification', {
        error: error.message,
        signature: signature?.substring(0, 10) + '...',
      });
      return false;
    }
  }

  /**
   * Verify custom webhook secret (for Shopify Flow apps)
   * @param providedSecret - Secret sent in request header or body
   * @returns boolean indicating if secret is valid
   */
  verifyCustomSecret(providedSecret: string): boolean {
    try {
      if (!providedSecret) {
        this.logger.warn('No custom secret provided for webhook verification');
        return false;
      }

      // Use timing-safe comparison to prevent timing attacks
      const isValid = crypto.timingSafeEqual(
        Buffer.from(this.webhookSecret, 'utf8'),
        Buffer.from(providedSecret, 'utf8'),
      );

      if (!isValid) {
        this.logger.warn('Custom webhook secret verification failed');
      }

      return isValid;
    } catch (error) {
      this.logger.error('Error during custom secret verification', {
        error: error.message,
      });
      return false;
    }
  }

  /**
   * Extract and verify webhook from request headers
   * @param headers - Request headers object
   * @param body - Raw request body
   * @returns boolean indicating if webhook is verified
   */
  verifyWebhookFromHeaders(headers: any, body: string | Buffer): boolean {
    // Try Shopify standard webhook verification first
    const shopifySignature = headers['x-shopify-hmac-sha256'];
    if (shopifySignature) {
      return this.verifyShopifyWebhook(body, shopifySignature);
    }

    // Try custom secret verification
    const customSecret =
      headers['x-webhook-secret'] ||
      headers['authorization']?.replace('Bearer ', '');
    if (customSecret) {
      return this.verifyCustomSecret(customSecret);
    }

    this.logger.warn('No webhook verification method found in headers');
    return false;
  }
}
